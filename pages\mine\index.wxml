<view class="container containermine">
  <diy-navbar :isFixed="true" bgColor="white" CustomBar='60'>
    <view slot="content">
      <view class="flex align-center flex-nowrap justify-start diygw-col-24 title-left">
        我的
      </view>
    </view>
  </diy-navbar>
  <view class="flex flex-wrap diygw-col-24 flex-direction-column flex12-clz">
    <view class="flex flex-wrap diygw-col-24 items-stretch flex10-clz">
      <view class="flex flex-wrap diygw-col-0 items-center" bind:tap="uploadAvatar">
        <image src="{{userInfo.avatar || siteinfo.constant.defalutAvatar1}}" class="image7-size diygw-image diygw-col-0 image-round" mode="widthFix"></image>
      </view>
      <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-between flex20-clz">
        <view class="flex flex-wrap diygw-col-24 justify-between items-center flex13-clz">
          <text class="diygw-col-0 text12-clz"> {{userInfo.name||'未登录'}} </text>
        </view>
        <view class="flex flex-wrap diygw-col-24 items-center flex14-clz">
          <text class="diygw-col-0"> {{userInfo.phone||'手机号待获取'}} </text>
        </view>
      </view>
      <view class="flex flex-wrap diygw-col-0 items-center">
        <text wx:if="{{userInfo.id}}" class="flex icon5 diygw-col-0 diy-icon-settings"></text>
        <button wx:else data-type="login" bindtap="redirect" class="btn-login">去登录</button>
      </view>
    </view>
  </view>
  <view wx:if="{{userInfo}}" class="flex align-center flex16-clz account-info">
    <view class="diygw-col-12 action-part">
      <view class="title">账户余额</view>
      <view class="bold">0</view>
      <!-- <view class="flex align-center justify-center"><text>提现</text><text class="flex icon6 diygw-col-0 diy-icon-titles"></text><text>明细</text></view> -->
    </view>
    <view class="diygw-col-12 action-part">
      <view class="title">我的评价</view>
      <view class="bold">{{userStats.averageRating || '暂无'}}</view>
      <view class="rating-detail" wx:if="{{userStats.totalReviews > 0}}">
        <text class="review-count">共{{userStats.totalReviews}}条评价</text>
      </view>
    </view>
  </view>
  <view wx:if="{{userInfo}}" class="clear-both flex flex-wrap diygw-col-24 flex-direction-column items-center flex16-clz">
    <view class="flex flex-wrap diygw-col-24 justify-between flex17-clz">
      <view class="flex flex-wrap diygw-col-0 justify-center items-center">
        <text class="diygw-text-line1 diygw-col-0 text13-clz"> 我的作品 </text>
      </view>
      <view class="flex flex-wrap diygw-col-0 items-center flex30-clz">
        <navigator url="/pages/mine/works/index">
          <text class="diygw-col-0 text32-clz"> 作品管理 </text>
          <text class="flex icon6 diygw-col-0 diy-icon-right"></text>
        </navigator>
      </view>
    </view>
    <scroll-view scroll-x class="flex scroll-view flex-wrap diygw-col-24 flex6-clz">
      <view class="flex flex-nowrap">
        <view class="flex diygw-col-0 items-stretch flex-nowrap flex46-clz">
          <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-center items-center">
            <image src="//xian7.zos.ctyun.cn/pet/static/tt.png" class="image7-size diygw-image diygw-col-0 image7-clz" mode="widthFix"></image>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
  <view class="vipbg">
    <image src='//xian7.zos.ctyun.cn/pet/static/memberbg.png' class="diygw-image diygw-col-24 image-vipbg" mode="widthFix" />
    <view class='vip-contain'>
      <view class="viptext">邀请好朋友，加入贝宠乐福</view>
    </view>
  </view>
  <view class="flex flex-wrap diygw-col-24 flex-direction-column flex38-clz">
    <view class="flex flex-wrap diygw-col-24">
      <!-- <view wx:if="{{userInfo}}" class="flex flex-wrap diygw-col-6 flex-direction-column items-center flex18-clz" data-type="photo" bind:tap="redirect">
        <image src="//xian7.zos.ctyun.cn/pet/static/ccpz.png" class="image-size diygw-image diygw-col-0 image-sm" mode="widthFix"></image>
        <text class="diygw-text-line1 diygw-col-0 text11-clz"> 出车拍照 </text>
      </view>
      <view wx:if="{{userInfo}}" class="flex flex-wrap diygw-col-6 flex-direction-column items-center flex18-clz" data-type="ticket" bind:tap="redirect">
        <image src="//xian7.zos.ctyun.cn/pet/static/wdfd.png" class="image-size diygw-image diygw-col-0 image-sm" mode="widthFix"></image>
        <text class="diygw-text-line1 diygw-col-0 text11-clz"> 我的罚单 </text>
      </view> -->
      <button wx:if="{{userInfo}}" open-type="contact" session-from="客服入口" class="flex flex-wrap diygw-col-6 flex-direction-column items-center flex18-clz card">
        <image src="//xian7.zos.ctyun.cn/pet/static/kefu.png" class="image-size diygw-image diygw-col-0 image-sm" mode="widthFix"></image>
        <text class="diygw-text-line1 diygw-col-0 text11-clz"> 在线客服 </text>
      </button>
      <view wx:if="{{userInfo}}" class="flex flex-wrap diygw-col-6 flex-direction-column items-center flex18-clz" data-type="complaint" bind:tap="redirect">
        <image src="//xian7.zos.ctyun.cn/pet/static/fankui.png" class="image-size diygw-image diygw-col-0 image-sm" mode="widthFix"></image>
        <text class="diygw-text-line1 diygw-col-0 text11-clz"> 建议反馈 </text>
      </view>
      <!-- </view>
    <view class="flex flex-wrap diygw-col-24"> -->
      <!-- <view class="flex flex-wrap diygw-col-6 flex-direction-column items-center flex18-clz">
        <image src="//xian7.zos.ctyun.cn/pet/static/huancun.png" class="image-size diygw-image diygw-col-0 image-sm" mode="widthFix"></image>
        <text class="diygw-text-line1 diygw-col-0 text11-clz"> 清除缓存 </text>
      </view> -->
      <view class="flex flex-wrap diygw-col-6 flex-direction-column items-center flex18-clz" data-type="user" bind:tap="redirect">
        <image src="//xian7.zos.ctyun.cn/pet/static/yonghux.png" class="image-size diygw-image diygw-col-0 image-sm" mode="widthFix"></image>
        <text class="diygw-text-line1 diygw-col-0 text11-clz"> 用户协议 </text>
      </view>
      <view class="flex flex-wrap diygw-col-6 flex-direction-column items-center flex18-clz" data-type="previate" bind:tap="redirect">
        <image src="//xian7.zos.ctyun.cn/pet/static/yinsi.png" class="image-size diygw-image diygw-col-0 image-sm" mode="widthFix"></image>
        <text class="diygw-text-line1 diygw-col-0 text11-clz"> 隐私协议 </text>
      </view>
      <!-- <view class="flex flex-wrap diygw-col-6 flex-direction-column items-center flex18-clz" data-type="invate" bind:tap="redirect">
        <image src="//xian7.zos.ctyun.cn/pet/static/yqyl.png" class="image-size diygw-image diygw-col-0 image-sm" mode="widthFix"></image>
        <text class="diygw-text-line1 diygw-col-0 text11-clz"> 邀请有礼 </text>
      </view> -->
    </view>
  </view>
  <custom-modal show="{{showModal}}" bind:confirm="onModalConfirm" title="{{modalTitle}}" content="{{modalContent}}"></custom-modal>
  <custom-tabbar currentActive='mine' />
</view>
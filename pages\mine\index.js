// pages/mine/index.js
import siteinfo from '../../siteinfo.js';
import userApi from '../../api/modules/user.js';
import Session from '../../common/Session.js';
Page({
	/**
	 * 页面的初始数据
	 */
	data: {
		siteinfo,
		userInfo: {},
		userStats: {
			averageRating: 0,
			totalReviews: 0,
		},
		showModal: false,
		modalTitle: '敬请期待',
		modalContent: '该功能正在开发中，我们正努力为您带来更好的体验！',
	},

	onLoad() {
		this.initUserInfo();
	},

	onShow() {
		// 每次显示页面时都更新用户信息
		this.updateUserInfo();
	},

	// 初始化用户信息
	initUserInfo() {
		const userInfo = Session.getUser();
		console.log('当前用户信息:', userInfo);
		if (!userInfo || !userInfo.id) {
			return wx.redirectTo({
				url: '/pages/login/index',
			});
		}
		this.setData({ userInfo });
		this.updateUserInfo();
	},

	// 更新用户信息和统计数据
	async updateUserInfo() {
		const userInfo = Session.getUser();
		if (!userInfo || !userInfo.id) {
			return;
		}

		try {
			// 并行获取用户信息和统计数据
			const [updatedUserInfo, userStats] = await Promise.all([
				userApi.getUserInfo(userInfo.id),
				userApi.getUserStats(userInfo.id)
			]);

			// 更新用户信息
			if (updatedUserInfo) {
				const newUserInfo = { ...userInfo, ...updatedUserInfo };
				Session.setUser(newUserInfo);
				this.setData({ userInfo: newUserInfo });
			}

			// 更新统计数据
			if (userStats) {
				this.setData({ userStats });
			}

			console.log('用户信息更新成功:', { updatedUserInfo, userStats });
		} catch (error) {
			console.error('更新用户信息失败:', error);
			// 静默失败，不影响用户体验
		}
	},

	redirect(evt) {
		let { type } = evt.currentTarget.dataset;
		let url;
		switch (type) {
			case 'user':
				url = '/pages/mine/userAgreement/index';
				break;
			case 'previate':
				url = '/pages/mine/privacyAgreement/index';
				break;
			case 'login':
				url = '/pages/login/index';
				break;
			case 'complaint':
				url = '/pages/complaint/index';
				break;
			default:
				break;
		}
		if (url) {
			wx.navigateTo({
				url,
			});
		} else {
			// 显示自定义模态框
			this.setData({
				showModal: true,
			});
		}
	},

	uploadAvatar() {
		this.uploadImage(
			this,
			'userInfo.avatar', // 想把url存入哪个字段
			[],
			`avatar/test.png`, // 上传的key,登录avatar/00001.png
			1
		);
	},

	// 模态框确认事件
	onModalConfirm() {
		this.setData({
			showModal: false,
		});
	},

	/**
	 * 页面上拉触底事件的处理函数
	 */
	onReachBottom() {},

	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage() {},
});
